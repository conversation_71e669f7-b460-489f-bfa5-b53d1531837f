rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isGuide() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'guide';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }
    
    function isValidTimestamp(timestamp) {
      return timestamp is timestamp;
    }
    
    // Users collection - user profiles and authentication data
    match /users/{userId} {
      // Users can read their own profile, admins can read all profiles
      allow read: if isOwner(userId) || isAdmin();
      
      // Users can create their own profile during registration
      allow create: if isOwner(userId) && 
                       isValidEmail(request.resource.data.email) &&
                       request.resource.data.role in ['user'] &&
                       isValidTimestamp(request.resource.data.createdAt) &&
                       isValidTimestamp(request.resource.data.updatedAt);
      
      // Users can update their own profile (except role), admins can update any profile
      allow update: if (isOwner(userId) && 
                        request.resource.data.role == resource.data.role &&
                        isValidTimestamp(request.resource.data.updatedAt)) ||
                       isAdmin();
      
      // Only admins can delete user profiles
      allow delete: if isAdmin();
    }
    
    // Tours collection - safari tour packages (public read, admin write)
    match /tours/{tourId} {
      // Anyone can read tours for browsing
      allow read: if true;
      
      // Only admins can create, update, or delete tours
      allow write: if isAdmin() && 
                      request.resource.data.title is string &&
                      request.resource.data.price is number &&
                      request.resource.data.price > 0 &&
                      isValidTimestamp(request.resource.data.updatedAt);
    }
    
    // Destinations collection - safari destinations (public read, admin write)
    match /destinations/{destinationId} {
      // Anyone can read destinations for browsing
      allow read: if true;
      
      // Only admins can create, update, or delete destinations
      allow write: if isAdmin() && 
                      request.resource.data.name is string &&
                      request.resource.data.country is string &&
                      isValidTimestamp(request.resource.data.updatedAt);
    }
    
    // Bookings collection - user bookings (user-specific access)
    match /bookings/{bookingId} {
      // Users can read their own bookings, admins can read all bookings
      allow read: if isOwner(resource.data.userId) || isAdmin();
      
      // Authenticated users can create bookings for themselves
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.userId &&
                       request.resource.data.tourId is string &&
                       request.resource.data.pricing.totalAmount is number &&
                       request.resource.data.pricing.totalAmount > 0 &&
                       isValidTimestamp(request.resource.data.createdAt);
      
      // Users can update their own pending bookings, admins can update any booking
      allow update: if (isOwner(resource.data.userId) && 
                        resource.data.status == 'pending' &&
                        request.resource.data.userId == resource.data.userId) ||
                       isAdmin();
      
      // Only admins can delete bookings
      allow delete: if isAdmin();
    }
    
    // Reviews collection - tour reviews (public read, authenticated write)
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if true;
      
      // Authenticated users can create reviews
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.userId &&
                       request.resource.data.rating is number &&
                       request.resource.data.rating >= 1 &&
                       request.resource.data.rating <= 5 &&
                       request.resource.data.tourId is string &&
                       isValidTimestamp(request.resource.data.createdAt);
      
      // Users can update their own reviews, admins can update any review
      allow update: if (isOwner(resource.data.userId) &&
                        request.resource.data.userId == resource.data.userId) ||
                       isAdmin();
      
      // Users can delete their own reviews, admins can delete any review
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }
    
    // Blog Posts collection - company blog (public read, admin write)
    match /blogPosts/{postId} {
      // Anyone can read published blog posts
      allow read: if resource.data.published == true || isAdmin();
      
      // Only admins can create, update, or delete blog posts
      allow write: if isAdmin() && 
                      request.resource.data.title is string &&
                      request.resource.data.content is string &&
                      isValidTimestamp(request.resource.data.updatedAt);
    }
    
    // Contact Messages collection - customer inquiries (admin access only)
    match /contactMessages/{messageId} {
      // Only admins can read contact messages
      allow read: if isAdmin();
      
      // Anyone can create contact messages (for contact form submissions)
      allow create: if request.resource.data.name is string &&
                       isValidEmail(request.resource.data.email) &&
                       request.resource.data.message is string &&
                       isValidTimestamp(request.resource.data.createdAt);
      
      // Only admins can update or delete contact messages
      allow update, delete: if isAdmin();
    }
    
    // Notifications collection - user notifications (user-specific access)
    match /notifications/{notificationId} {
      // Users can read their own notifications, admins can read all
      allow read: if isOwner(resource.data.userId) || isAdmin();
      
      // Only admins and the system can create notifications
      allow create: if isAdmin() && 
                       request.resource.data.userId is string &&
                       request.resource.data.title is string &&
                       isValidTimestamp(request.resource.data.createdAt);
      
      // Users can update their own notifications (mark as read), admins can update any
      allow update: if (isOwner(resource.data.userId) &&
                        request.resource.data.userId == resource.data.userId) ||
                       isAdmin();
      
      // Only admins can delete notifications
      allow delete: if isAdmin();
    }
    
    // Payment Transactions collection - financial records (admin access only)
    match /paymentTransactions/{transactionId} {
      // Only admins can access payment transactions
      allow read, write: if isAdmin();
    }
    
    // Custom Tour Requests collection - user tour requests
    match /customTourRequests/{requestId} {
      // Users can read their own requests, admins can read all
      allow read: if isOwner(resource.data.userId) || isAdmin();
      
      // Anyone can create custom tour requests
      allow create: if request.resource.data.name is string &&
                       isValidEmail(request.resource.data.email) &&
                       request.resource.data.duration is number &&
                       request.resource.data.participants is number &&
                       isValidTimestamp(request.resource.data.createdAt);
      
      // Only admins can update or delete custom tour requests
      allow update, delete: if isAdmin();
    }
    
    // Gallery Images collection - photo gallery (public read, admin write)
    match /galleryImages/{imageId} {
      // Anyone can read gallery images
      allow read: if true;
      
      // Only admins can manage gallery images
      allow write: if isAdmin() && 
                      request.resource.data.title is string &&
                      request.resource.data.imageUrl is string &&
                      isValidTimestamp(request.resource.data.updatedAt);
    }
    
    // Accommodations collection - lodges and camps (public read, admin write)
    match /accommodations/{accommodationId} {
      // Anyone can read accommodations
      allow read: if true;
      
      // Only admins can manage accommodations
      allow write: if isAdmin() && 
                      request.resource.data.name is string &&
                      request.resource.data.type in ['lodge', 'camp', 'hotel', 'guesthouse'] &&
                      isValidTimestamp(request.resource.data.updatedAt);
    }
    
    // Activities collection - safari activities (public read, admin write)
    match /activities/{activityId} {
      // Anyone can read activities
      allow read: if true;
      
      // Only admins can manage activities
      allow write: if isAdmin();
    }
    
    // Guides collection - tour guides (public read, admin/guide write)
    match /guides/{guideId} {
      // Anyone can read guide profiles
      allow read: if true;
      
      // Admins can manage all guides, guides can update their own profile
      allow create, delete: if isAdmin();
      allow update: if isAdmin() || (isGuide() && isOwner(resource.data.userId));
    }
    
    // Travel Guides collection - travel information (public read, admin write)
    match /travelGuides/{guideId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Packing Lists collection - travel packing guides (public read, admin write)
    match /packingLists/{listId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Wildlife Sightings collection - wildlife tracking (guide/admin write, public read)
    match /wildlifeSightings/{sightingId} {
      allow read: if true;
      allow create: if isGuide() || isAdmin();
      allow update, delete: if isAdmin();
    }
    
    // Chat Messages collection - customer support chat (user-specific access)
    match /chatMessages/{messageId} {
      // Users can read messages in their conversations, admins can read all
      allow read: if isOwner(resource.data.userId) || isAdmin();
      
      // Authenticated users can create messages
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.userId &&
                       request.resource.data.message is string;
      
      // Only admins can update or delete chat messages
      allow update, delete: if isAdmin();
    }
    
    // Weather Data collection - weather information (public read, admin write)
    match /weatherData/{dataId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Virtual Tours collection - 360° tours (public read, admin write)
    match /virtualTours/{tourId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Content Pages collection - CMS pages (public read, admin write)
    match /contentPages/{pageId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Wishlists collection - user wishlists (user-specific access)
    match /wishlists/{wishlistId} {
      // Users can access their own wishlists, admins can access all
      allow read: if isOwner(resource.data.userId) || isAdmin();
      
      // Users can create their own wishlists
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.userId;
      
      // Users can update their own wishlists, admins can update any
      allow update: if (isOwner(resource.data.userId) &&
                        request.resource.data.userId == resource.data.userId) ||
                       isAdmin();
      
      // Users can delete their own wishlists, admins can delete any
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }
    
    // Tour Packages collection - package deals (public read, admin write)
    match /tourPackages/{packageId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Tour Availability collection - booking availability (public read, admin write)
    match /tourAvailability/{availabilityId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Default deny rule for any unmatched paths
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
